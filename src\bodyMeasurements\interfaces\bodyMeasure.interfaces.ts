export interface IMeasurementRecord {
  value: number;
  recordedAt: Date;
}

export interface IMeasurementGoal {
  target: number;
  bodyPart: string;
}

export interface IBodyMeasurement {
  _id: string;
  userId: string;
  neck?: IMeasurementRecord;
  chest?: IMeasurementRecord;
  shoulder?: IMeasurementRecord;
  leftArm?: IMeasurementRecord;
  rightArm?: IMeasurementRecord;
  waist?: IMeasurementRecord;
  abdomen?: IMeasurementRecord;
  hip?: IMeasurementRecord;
  leftThigh?: IMeasurementRecord;
  rightThigh?: IMeasurementRecord;
  leftCalf?: IMeasurementRecord;
  rightCalf?: IMeasurementRecord;
  goals?: IMeasurementGoal;
  customMeasurements?: Map<string, IMeasurementRecord>;
  createdAt: Date;
  updatedAt: Date;
}
