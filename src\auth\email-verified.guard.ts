import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schema/users.schema';
import { IUser } from 'src/users/interfaces/users.interface';

@Injectable()
export class EmailVerifiedGuard implements CanActivate {
  constructor(
    @InjectModel(Users.name) private userModel: Model<IUser>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user._id) {
      throw new ForbiddenException('User not authenticated');
    }

    // Fetch the latest user data to check email verification status
    const userData = await this.userModel.findById(user._id);
    
    if (!userData) {
      throw new ForbiddenException('User not found');
    }

    if (!userData.isEmailVerified) {
      throw new ForbiddenException(
        'Email verification required. Please verify your email address to access this resource.'
      );
    }

    return true;
  }
}
