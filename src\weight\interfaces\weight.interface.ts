export interface IWeight {
  _id: string;
  userId: string;
  weight: number;
  bmi?: number;
  bodyFatPercentage?: number;
  measuredAt: Date;
  inputMethod: 'manual' | 'device';
  deviceId?: string;
  comment?: string;
  goal?: IWeightGoal;
  createdAt: Date;
  updatedAt: Date;
}

export interface IWeightGoal {
    goalType: 'lose' | 'gain' | 'maintain';
    targetWeight: number;
    targetBodyFatPercentage?: number;
    startDate: Date;
    targetDate?: Date;
    reminderEnabled?: boolean;
    reminderTime?: string;
}