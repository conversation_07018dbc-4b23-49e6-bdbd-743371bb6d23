import { PartialType } from "@nestjs/swagger";
import { CreateUserDto } from "./createUser.dto";
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsObject } from 'class-validator';
import { IMeasurementReminders, IHealthAssessment, IDiabetesInfo, IBloodPressureInfo, IMentalHealthInfo, ISleepInfo, IFitnessInfo, IHealthAssessmentData } from "../interfaces/users.interface";

export class UpdateUserDto extends PartialType(CreateUserDto){

    @ApiProperty({
        description: 'User\'s health assessment information including physical measurements and health conditions',
        example: {
            ageRange: '25-34',
            height: 175,
            weight: 70,
            bmi: 22.9,
            bodyFatPercentage: 15.5,
            conditionsToManage: ['Diabetes', 'Hypertension']
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    healthAssessment?: IHealthAssessment;

    @ApiProperty({
        description: 'Diabetes-specific information including type, medication, and glucose levels',
        example: {
            diabetesType: 'Type 2',
            diabetesDiagnosedSince: '2020',
            takesMedication: true,
            medications: ['Metformin', 'Insulin'],
            recentHbA1c: 7.2,
            rememberHbA1c: true,
            lastTestDuration: '3 months',
            recentFastingGlucose: 120,
            diabetesMotivationLevel: 8
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    diabetesInfo?: IDiabetesInfo;

    @ApiProperty({
        description: 'Blood pressure and cardiovascular health information',
        example: {
            hyperTension: true,
            diagonisedYear: 2019,
            bpSystolic: 140,
            bpDiastolic: 90,
            heartRate: 75,
            rememberBp: true
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    bloodPressureInfo?: IBloodPressureInfo;

    @ApiProperty({
        description: 'Mental health assessment including anxiety levels and emotional patterns',
        example: {
            comorbidities: ['Anxiety', 'Depression'],
            anxietyLevel: 'Moderate',
            commonEmotions: ['Stressed', 'Worried', 'Hopeful']
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    mentalHealthInfo?: IMentalHealthInfo;

    @ApiProperty({
        description: 'Sleep quality and patterns information',
        example: {
            sleepQualityRating: 'Fair',
            troubleFallingAsleep: 'Sometimes',
            sleepCondition: 'Insomnia'
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    sleepInfo?: ISleepInfo;

    @ApiProperty({
        description: 'Fitness and exercise-related information',
        example: {
            fitnessLevel: 'Moderate',
            motivationInsightsAccepted: true
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    fitnessInfo?: IFitnessInfo;

    @ApiProperty({
        description: 'Measurement reminder preferences for health tracking',
        example: {
            bpMeasurementReminders: true,
            bloodSugarMeasurementReminders: true
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    Reminders?: IMeasurementReminders;

    @ApiProperty({
        description: 'Comprehensive health assessment data including all health-related information',
        example: {
            gender: 'Male',
            ageRange: '25-34',
            weight: '70',
            height: '175',
            bmi: '22.9',
            conditionsToManage: ['Diabetes', 'Hypertension'],
            diabetesType: 'Type 2',
            diabetesDiagnosedSince: '2020',
            medicationStatus: 'Yes',
            medicationType: ['Metformin', 'Insulin'],
            glycemicMonitoring: {
                hba1c: '7.2',
                fastingGlucose: '120',
                lastTestInterval: '3 months',
                hba1cNotRemembered: false,
                lastTestMoreThanYear: false
            },
            hypertensionStatus: 'Yes',
            hypertensionSince: '2019',
            bloodPressure: {
                systolic: '140',
                diastolic: '90',
                pulse: '75',
                bpNotRemembered: false
            },
            comorbidities: ['Anxiety'],
            stressLevel: 'Moderate',
            emotion: 'Stressed',
            sleepQuality: 'Fair',
            fallAsleep: 'Sometimes',
            fallAsToNightleep: 'Rarely',
            diabetesLevel: 'Moderate',
            fitnessLevel: 'Low',
            lifeCraft: {
                analyzing: true,
                plan: false
            },
            reminders: {
                bloodPressure: true
            }
        },
        required: false
    })
    @IsOptional()
    @IsObject()
    healthAssessmentData?: IHealthAssessmentData;

};