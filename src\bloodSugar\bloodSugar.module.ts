import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { BloodSugar, BloodSugarSchema } from "./schema/bloodSugar.schema";
import { AuthModule } from "src/auth/auth.module";
import { BloodSugarService } from "./bloodSugar.service";
import { BloodSugarController } from "./bloodSugar.controller";

@Module({
    imports:[
        MongooseModule.forFeature([
            {name:BloodSugar.name,schema:BloodSugarSchema}
        ]),
        AuthModule,
    ],
    providers:[BloodSugarService],
    controllers:[BloodSugarController],
    exports:[BloodSugarService]
})
export class BloodSugarModule{}