import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the user',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;

    @ApiProperty({
        description: 'User\'s first name',
        example: '<PERSON>',
    })
    firstName: string;

    @ApiProperty({
        description: 'User\'s last name',
        example: 'Doe',
    })
    lastName: string;

    @ApiProperty({
        description: 'User\'s email address',
        example: '<EMAIL>',
    })
    email: string;

    @ApiProperty({
        description: 'User\'s mobile phone number',
        example: '+**********',
    })
    mobileNumber: string;

    @ApiProperty({
        description: 'User\'s gender',
        example: 'Male',
        enum: ['Male', 'Female', 'Other'],
    })
    gender: string;

    @ApiProperty({
        description: 'User\'s date of birth',
        example: '1990-01-15',
    })
    dob: string;

    @ApiProperty({
        description: 'Whether the user\'s account is verified',
        example: false,
        default: false,
    })
    isVerified: boolean;

    @ApiProperty({
        description: 'Timestamp when the user was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Timestamp when the user was last updated',
        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}
