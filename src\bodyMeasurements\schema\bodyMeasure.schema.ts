import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

@Schema({ _id: false })
export class MeasurementRecord {
  @Prop({ type: Number }) 
  value: number;

  @Prop({ type: Date, default: Date.now })
  recordedAt: Date;
}

@Schema({ _id: false })
export class MeasurementGoal {
  @Prop({ type: Number }) 
  target: number;

  @Prop({ type: String })
  bodyPart: string;
}

@Schema({ timestamps: true })
export class BodyMeasurement {

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Users' })
  userId: string;

  @Prop({ type: MeasurementRecord, required:false })
  neck?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  chest?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  shoulder?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  leftArm?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  rightArm?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  waist?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  abdomen?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  hip?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  leftThigh?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  rightThigh?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  leftCalf?: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:false })
  rightCalf?: MeasurementRecord;

  @Prop({ type: MeasurementGoal, required:false })
  goals?: MeasurementGoal;

  @Prop({
    type: Map,
    of: {
      value: { type: Number, required: false },
      recordedAt: { type: Date, default: Date.now }
    },
    required: false
  })
  customMeasurements?: Map<string, MeasurementRecord>;
}

export const BodyMeasurementSchema = SchemaFactory.createForClass(BodyMeasurement);
