/**
 * Simple in-memory rate limiter for email verification requests
 * In production, consider using Redis or a database for persistence
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private requests: Map<string, RateLimitEntry> = new Map();
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = 3, windowMs: number = 15 * 60 * 1000) { // 3 requests per 15 minutes
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  /**
   * Check if a request is allowed for the given identifier
   * @param identifier - Usually email address or IP
   * @returns Object with isAllowed boolean and remaining time if blocked
   */
  isAllowed(identifier: string): { isAllowed: boolean; remainingTime?: number } {
    const now = Date.now();
    const entry = this.requests.get(identifier);

    // Clean up expired entries
    this.cleanup();

    if (!entry) {
      // First request
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs,
      });
      return { isAllowed: true };
    }

    if (now > entry.resetTime) {
      // Window has expired, reset
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + this.windowMs,
      });
      return { isAllowed: true };
    }

    if (entry.count >= this.maxRequests) {
      // Rate limit exceeded
      return {
        isAllowed: false,
        remainingTime: entry.resetTime - now,
      };
    }

    // Increment count
    entry.count++;
    return { isAllowed: true };
  }

  /**
   * Clean up expired entries to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.requests.entries()) {
      if (now > entry.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * Reset rate limit for a specific identifier (useful for testing)
   */
  reset(identifier: string): void {
    this.requests.delete(identifier);
  }

  /**
   * Get remaining requests for an identifier
   */
  getRemainingRequests(identifier: string): number {
    const entry = this.requests.get(identifier);
    if (!entry || Date.now() > entry.resetTime) {
      return this.maxRequests;
    }
    return Math.max(0, this.maxRequests - entry.count);
  }
}

// Export singleton instance
export const emailVerificationRateLimiter = new RateLimiter(3, 15 * 60 * 1000); // 3 requests per 15 minutes

/**
 * Format remaining time in a human-readable format
 */
export function formatRemainingTime(ms: number): string {
  const minutes = Math.ceil(ms / (60 * 1000));
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  const hours = Math.ceil(minutes / 60);
  return `${hours} hour${hours !== 1 ? 's' : ''}`;
}
