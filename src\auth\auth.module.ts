import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from "@nestjs/jwt";
import { MongooseModule } from "@nestjs/mongoose";
import { Users, UsersSchema } from "src/users/schema/users.schema";
import { UsersModule } from "src/users/users.module";
import { AuthGuard } from "./auth.guard";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { Otp, OtpSchema } from "src/otp/schema/otp.schema";
import { OtpService } from "src/otp/otp.service";

@Module({
    imports:[
        ConfigModule,
       forwardRef(() => UsersModule),
        MongooseModule.forFeature([
            {name:Users.name,schema:UsersSchema},
             { name: Otp.name, schema: OtpSchema },
        ]),
        JwtModule.registerAsync({
             imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '100y' },
      }),
      inject: [ConfigService],
        })
    ],
    controllers:[AuthController],
    providers:[AuthService,AuthGuard,OtpService],
     exports: [AuthService, AuthGuard, JwtModule,OtpService],
})
export class AuthModule{}