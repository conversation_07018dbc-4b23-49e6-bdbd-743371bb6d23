import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { WeightRecord } from "./schema/weight.schema";
import { Model } from "mongoose";
import { IWeight } from "./interfaces/weight.interface";
import { UpdateWeightDto } from "./dto/updateWeight.dto";
import { AddWeightDto } from "./dto/AddWeight.dto";

@Injectable()
export class WeightService {
    constructor(@InjectModel(WeightRecord.name) private weightModel: Model<IWeight>) {}

    async create(weightDto: AddWeightDto): Promise<IWeight> {
        try {
            const record = await this.weightModel.create(weightDto);
            await record.populate({ path: 'userId', select: '-password' });
            return record;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to create weight record: ${error.message}`);
        }
    }
    async findAll(): Promise<IWeight[]> {
        try {
            return await this.weightModel.find().populate({ path: 'userId', select: '-password' }).exec();
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to fetch weight records: ${error.message}`);
        }
    }
    async findOne(id: string): Promise<IWeight> {
        try {
            const weight = await this.weightModel.findById(id).populate({ path: 'userId', select: '-password' }).exec();
            if (!weight) {
                throw new Error('Weight record not found');
            }
            return weight;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to fetch weight record: ${error.message}`);
        }
    }
    async update(id: string, weightDto: UpdateWeightDto): Promise<IWeight> {
        try {
            // First, get the existing weight record
            const existingWeight = await this.weightModel.findById(id).exec();
            if (!existingWeight) {
                throw new Error('Weight record not found');
            }

            // Build update object with proper nested field handling
            const updateData: any = {};

            // Handle regular fields
            Object.keys(weightDto).forEach(key => {
                if (key !== 'goal' && weightDto[key] !== undefined) {
                    updateData[key] = weightDto[key];
                }
            });

            // Handle nested goal object updates properly
            if (weightDto.goal) {
                if (existingWeight.goal) {
                    // Merge existing goal with new goal data using dot notation for nested updates
                    Object.keys(weightDto.goal).forEach(goalKey => {
                        if (weightDto.goal[goalKey] !== undefined) {
                            updateData[`goal.${goalKey}`] = weightDto.goal[goalKey];
                        }
                    });
                } else {
                    // If no existing goal, set the entire goal object
                    updateData.goal = weightDto.goal;
                }
            }

            // Use $set operator to ensure nested fields are properly updated
            const weight = await this.weightModel.findByIdAndUpdate(
                id,
                { $set: updateData },
                { new: true, runValidators: true }
            ).populate({ path: 'userId', select: '-password' }).exec();

            if (!weight) {
                throw new Error('Weight record not found');
            }
            return weight;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to update weight record: ${error.message}`);
        }
    }
    async delete(id: string): Promise<string> {
        try {
            const weight = await this.weightModel.findByIdAndDelete(id).exec();
            if (!weight) {
                throw new Error('Weight record not found');
            }
            return 'Weight record deleted';
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to delete weight record: ${error.message}`);
        }
    }
}