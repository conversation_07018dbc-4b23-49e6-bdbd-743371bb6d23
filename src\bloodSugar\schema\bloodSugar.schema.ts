import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';


@Schema({ timestamps: true })
export class BloodSugar {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Users', required: true })
  userId: string;

  @Prop({ required: false })
  glycemic?: number; 

  @Prop({ required: false })
  ketone?: number; 

  @Prop({
    type: String,
    enum: ['pre-meal','post-meal','fasting','bedTime'],
    required: false,
  })
  measurementType?: string;

  @Prop({
    type: String,
    enum: ['Manual', 'Device', 'Camera'],
    required: false,
  })
  source?: string; 

  @Prop({ type: String, default: null })
  deviceName?: string; 

  @Prop({ type: String, default: null })
  comment?: string;

  @Prop({ type: Date, default: Date.now })
  measurementDate?: string; 

  @Prop({type:[String],required:false})
  preMeasurementActivities?: string[];

  @Prop({ type: String, default: null })
  customActivityNote?: string;


}

export const BloodSugarSchema = SchemaFactory.createForClass(BloodSugar);
