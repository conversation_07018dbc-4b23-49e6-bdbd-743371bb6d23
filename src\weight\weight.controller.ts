import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { WeightService } from "./weight.service";
import { AuthGuard } from "src/auth/auth.guard";
import { WeightResponseDto } from "./dto/wightResponse.dto";
import { UpdateWeightDto } from "./dto/updateWeight.dto";
import { IMeta, IQueryResponse } from "src/interfaces/query-response";
import { AddWeightDto } from "./dto/AddWeight.dto";

@ApiTags('Weight')
@UseGuards(AuthGuard)
@ApiBearerAuth('Bearer-auth')
@Controller('weight')
export class WeightController {
    constructor(private readonly weightService:WeightService){}

    @Post()
    @ApiOperation({
        summary: 'Create weight record',
        description: 'Create a new weight record',
    })
    @ApiBody({
        type: AddWeightDto,
        description: 'Weight record data',
    })
    @ApiResponse({
        status: 201,
        description: 'Weight record created',
        type: WeightResponseDto,
    })
    async createWeight(@Body() weightDto: AddWeightDto) {
        return this.weightService.create(weightDto);
    }



    @Get()
    @ApiOperation({
        summary: 'Get all weight records',
        description: 'Retrieve all weight records',
    })
    @ApiResponse({
        status: 200,
        description: 'Weight records found',
        type: [WeightResponseDto],
    })
    async getAllWeightRecords():Promise<IQueryResponse<WeightResponseDto>> {
        const weightRecords = await this.weightService.findAll();
        const meta: IMeta = {
            totalSize: weightRecords.length,
            // chunk: 1,
            // page: 1,
            // limit: weightRecords.length,
        };
        return { meta, results: weightRecords };
    }



    @Get(':id')
    @ApiOperation({
        summary: 'Get weight record by ID',
        description: 'Retrieve weight record by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Weight record found',
        type: WeightResponseDto,
    })
    async getWeightRecordById(@Param('id') id: string) {
        return this.weightService.findOne(id);
    }



    @Patch(':id')
    @ApiOperation({
        summary: 'Update weight record by ID',
        description: 'Update weight record by its unique identifier',
    })
    @ApiBody({
        type: UpdateWeightDto,
        description: 'Weight record data',
    })
    @ApiResponse({
        status: 200,
        description: 'Weight record updated',
        type: WeightResponseDto,
    })
    async updateWeightRecord(
        @Param('id') id: string,
        @Body() weightDto: UpdateWeightDto,
    ) {
        return this.weightService.update(id, weightDto);
    }


    
    @Delete(':id')
    @ApiOperation({
        summary: 'Delete weight record by ID',
        description: 'Delete weight record by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Weight record deleted',
        type: WeightResponseDto,
    })
    async deleteWeightRecord(@Param('id') id: string) {
        return this.weightService.delete(id);
    }


}