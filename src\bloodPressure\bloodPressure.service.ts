import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { BloodPressure } from "./schema/bloodPressure.schema";
import { IBloodPressure } from "./interfaces/bloodPressure.interface";
import { Model } from "mongoose";
import { UpdateBloodPressureRecordDto } from "./dto/updateBloodPressureRecord.dto";
import { AddBloodPressureRecordDto } from "./dto/addBloodPressureRecord.dto";

@Injectable()
export class BloodPressureService {
    constructor(@InjectModel(BloodPressure.name) private bloodPressureModel: Model<IBloodPressure>) {}

    async create(bloodPressureDto: AddBloodPressureRecordDto): Promise<IBloodPressure> {
        try {
            const record = await this.bloodPressureModel.create(bloodPressureDto);
            await record.populate({ path: 'userId', select: '-password' });
            return record;
        } catch (error) {
            throw new InternalServerErrorException(`Failed to create blood pressure measurement: ${error.message}`);
        }
    }

    async findAll(): Promise<IBloodPressure[]> {
        try {
            return await this.bloodPressureModel.find().populate({path:'userId',select:'-password'}).exec();
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to fetch blood pressure measurements: ${error.message}`);
        }
    }
    async findOne(id: string): Promise<IBloodPressure> {
        try {
            const bloodPressure = await this.bloodPressureModel.findById(id).populate({path:'userId',select:'-password'}).exec();
            if (!bloodPressure) {
                throw new Error('Blood pressure measurement not found');
            }
            return bloodPressure;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to fetch blood pressure measurement: ${error.message}`);
        }
    }
    async update(id: string, bloodPressureDto: UpdateBloodPressureRecordDto): Promise<IBloodPressure> {
        try {
            const bloodPressure = await this.bloodPressureModel.findByIdAndUpdate(id, bloodPressureDto, { new: true }).populate({path:'userId',select:'-password'}).exec();
            if (!bloodPressure) {
                throw new Error('Blood pressure measurement not found');
            }
            return bloodPressure;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to update blood pressure measurement: ${error.message}`);
        }
    }
    async delete(id: string): Promise<string> {
        try {
            const bloodPressure = await this.bloodPressureModel.findByIdAndDelete(id).exec();
            if (!bloodPressure) {
                throw new Error('Blood pressure measurement not found');
            }
            return 'Blood pressure measurement deleted';
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to delete blood pressure measurement: ${error.message}`);
        }
    }

}