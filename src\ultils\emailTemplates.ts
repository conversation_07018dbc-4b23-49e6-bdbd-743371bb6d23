/**
 * Email verification templates for HTML and plain text formats
 */

export interface EmailVerificationData {
  email: string;
  verificationUrl: string;
  appName?: string;
}

/**
 * Generate HTML email template for email verification
 */
export function generateVerificationEmailHtml(data: EmailVerificationData): string {
  const { email, verificationUrl, appName = 'VitaeChek' } = data;
  
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - ${appName}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
        }
        .content {
            font-size: 16px;
            margin-bottom: 30px;
        }
        .verify-button {
            display: inline-block;
            background-color: #2c5aa0;
            color: #ffffff;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            font-size: 16px;
            margin: 20px 0;
            text-align: center;
        }
        .verify-button:hover {
            background-color: #1e3d6f;
        }
        .alternative-link {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
            font-size: 14px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">${appName}</div>
            <h1 class="title">Verify Your Email Address</h1>
        </div>
        
        <div class="content">
            <p>Hello,</p>
            <p>Thank you for registering with ${appName}! To complete your registration and start using your account, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center;">
                <a href="${verificationUrl}" class="verify-button">Verify Email Address</a>
            </div>
            
            <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
            <div class="alternative-link">
                ${verificationUrl}
            </div>
            
            <div class="warning">
                <strong>Important:</strong> This verification link will expire in 24 hours for security reasons. If you don't verify your email within this time, you'll need to request a new verification email.
            </div>
            
            <p>If you didn't create an account with ${appName}, you can safely ignore this email.</p>
        </div>
        
        <div class="footer">
            <p>This is an automated message from ${appName}. Please do not reply to this email.</p>
            <p>&copy; ${new Date().getFullYear()} ${appName}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;
}

/**
 * Generate plain text email template for email verification
 */
export function generateVerificationEmailText(data: EmailVerificationData): string {
  const { email, verificationUrl, appName = 'VitaeChek' } = data;
  
  return `
${appName} - Verify Your Email Address

Hello,

Thank you for registering with ${appName}! To complete your registration and start using your account, please verify your email address.

Click the following link to verify your email:
${verificationUrl}

IMPORTANT: This verification link will expire in 24 hours for security reasons. If you don't verify your email within this time, you'll need to request a new verification email.

If you didn't create an account with ${appName}, you can safely ignore this email.

---
This is an automated message from ${appName}. Please do not reply to this email.
© ${new Date().getFullYear()} ${appName}. All rights reserved.
`;
}

/**
 * Generate email subject for verification email
 */
export function generateVerificationEmailSubject(appName: string = 'VitaeChek'): string {
  return `Verify your email address - ${appName}`;
}
