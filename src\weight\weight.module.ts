import { Modu<PERSON> } from "@nestjs/common";
import { AuthModule } from "src/auth/auth.module";
import { WeightRecord, WeightRecordSchema } from "./schema/weight.schema";
import { MongooseModule } from "@nestjs/mongoose";
import { WeightController } from "./weight.controller";
import { WeightService } from "./weight.service";

@Module({
    imports: [
        MongooseModule.forFeature([
            {name:WeightRecord.name,schema:WeightRecordSchema}
        ]),
        AuthModule,
       

    ],
    controllers:[WeightController],
    providers:[WeightService],
    exports:[WeightService],
})
export class WeightModule{}