import * as crypto from 'crypto';

/**
 * Generate a cryptographically secure random token for email verification
 * @param length - Length of the token (default: 32 bytes = 64 hex characters)
 * @returns A secure random token as hex string
 */
export function generateVerificationToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate token expiry date (24 hours from now by default)
 * @param hours - Hours until expiry (default: 24)
 * @returns Date object representing expiry time
 */
export function generateTokenExpiry(hours: number = 24): Date {
  const expiry = new Date();
  expiry.setHours(expiry.getHours() + hours);
  return expiry;
}

/**
 * Check if a token has expired
 * @param expiry - Token expiry date
 * @returns true if token has expired, false otherwise
 */
export function isTokenExpired(expiry: Date): boolean {
  return new Date() > expiry;
}

/**
 * Generate both token and expiry date
 * @param tokenLength - Length of the token (default: 32 bytes)
 * @param expiryHours - Hours until expiry (default: 24)
 * @returns Object containing token and expiry date
 */
export function generateTokenWithExpiry(tokenLength: number = 32, expiryHours: number = 24): {
  token: string;
  expiry: Date;
} {
  return {
    token: generateVerificationToken(tokenLength),
    expiry: generateTokenExpiry(expiryHours),
  };
}
