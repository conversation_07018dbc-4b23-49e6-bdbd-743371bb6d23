import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type WeightRecordDocument = WeightRecord & Document;


@Schema({ _id: false }) 
export class WeightGoal {
  @Prop({ enum: ['lose', 'gain', 'maintain'], required: true })
  goalType: 'lose' | 'gain' | 'maintain';

  @Prop({ required: true })
  targetWeight: number;

  @Prop()
  targetBodyFatPercentage?: number;

  @Prop()
  startDate: Date;

  @Prop()
  targetDate?: Date;

  @Prop({ default: false })
  reminderEnabled?: boolean;

  @Prop()
  reminderTime?: string; // Format: "HH:mm"
}


@Schema({ timestamps: true })
export class WeightRecord {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  weight: number;

  @Prop()
  bmi?: number;

  @Prop()
  bodyFatPercentage?: number;

  @Prop()
  measuredAt: Date;

  @Prop({ enum: ['manual', 'device'], default: 'manual' })
  inputMethod: 'manual' | 'device';

  @Prop()
  deviceId?: string;

  @Prop()
  comment?: string;

  
  @Prop({ type: WeightGoal, required: false })
  goal?: WeightGoal;
}

export const WeightRecordSchema = SchemaFactory.createForClass(WeightRecord);
