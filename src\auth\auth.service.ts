import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from 'src/users/users.service';
import { IAuth, ISignUp } from './interfaces/auth.interfaces';
import { IUser, IUserSignUp } from 'src/users/interfaces/users.interface';
import { CreateUserDto } from 'src/users/dto/createUser.dto';
import { signInDto } from './dto/signIn.dto';
import * as bcrypt from 'bcrypt';
import { OtpService } from 'src/otp/otp.service';
import { otpgenerator } from 'src/ultils/otpHelper';
import { ForgotPasswordDto } from './dto/forget-password.dto';
import { MailerService } from '@nestjs-modules/mailer';
import { CreateOtpDto } from '../otp/dto/create-otp.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Users } from 'src/users/schema/users.schema';
import { Model } from 'mongoose';
import { VerifyOtpDTO } from './dto/verify-otp.dto';
import { signUpDto } from './dto/signUp.dto';
import { generateTokenWithExpiry, isTokenExpired } from 'src/ultils/tokenHelper';
import { generateVerificationEmailHtml, generateVerificationEmailText, generateVerificationEmailSubject } from 'src/ultils/emailTemplates';
import { ResendVerificationDto } from './dto/resend-verification.dto';
import { ConfigService } from '@nestjs/config';
import { emailVerificationRateLimiter, formatRemainingTime } from 'src/ultils/rateLimiter';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(Users.name) private userModel: Model<IUser>,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly otpService: OtpService,
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) {}

  async signUp(signUpDto: signUpDto): Promise<ISignUp> {
    const user: Omit<IUserSignUp, 'password'> =
      await this.usersService.createUser(signUpDto);

    // Send verification email after successful registration
    // try {
    //   await this.sendVerificationEmail(user.email);
    // } catch (error) {
    //   // Log the error but don't fail the registration
    //   console.error('Failed to send verification email during signup:', error);
    // }

    const payload = { name: user.email, _id: user._id };
    const accessToken = await this.jwtService.signAsync(payload);
    return {
      ...user,
      accessToken,
    };
  }

  async signIn(signInDto: signInDto): Promise<IAuth> {
    const user = await this.usersService.findByEmail(
      signInDto.email.toLowerCase(),
    );
    if (!user) {
      throw new Error('Invalid credentials');
    }
    const userWithPassword = await this.usersService.findByEmailWithPassword(
      signInDto.email.toLowerCase(),
    );
    const isMatch = await bcrypt.compare(
      signInDto.password,
      userWithPassword.password,
    );
    if (!isMatch) {
      throw new Error('Invalid credentials');
    }
    const payload = { name: user.firstName || user.email, _id: user._id };
    const accessToken = await this.jwtService.signAsync(payload);
    return {
      ...user,
      accessToken,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    try {
      const email = forgotPasswordDto.email.toLowerCase();
      const user = await this.usersService.findByEmail(email);

      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Invalid Email',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate and store OTP
      const otp = otpgenerator();

      const opt = await this.otpService.create({
        email: user.email,
        otp: otp.toString(),
      });

      console.log('otp', opt);

      await this.mailerService.sendMail({
        to: email,
        subject: 'Reset Password OTP',
        text: `Your OTP for resetting password is ${otp}.`,
      });

      return { message: `OTP sent to ${email}` };
    } catch (error) {
      console.error('Error in forgotPassword:', error);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Something went wrong. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDTO): Promise<any> {
    const email = verifyOtpDto.email.toLowerCase();
    const otpFound = await this.otpService.findOne(email, verifyOtpDto.otp);

    if (!otpFound) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid OTP',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
    this.otpService.delete(otpFound._id as string);

    return {
      message: 'OTP Verified',
      status: 200,
    };
  }

 async resetPassword(dto: ResetPasswordDto): Promise<any> {
    // Reset password
    const hashPassword = await bcrypt.hash(dto.password, 10);
    const email = dto.email.toLowerCase();
    const result = await this.usersService.update(email, {
      password: hashPassword,
    });
    if (!result) {
      throw new Error('Update failed: User not found');
    }

    return {
      message: 'PASSWORD RESTORED - 200',
    };
  }

   async changePassword(
    userId: string,
    changePasswordDto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const { oldPassword, newPassword } = changePasswordDto;

    if (!oldPassword || !newPassword) {
      throw new BadRequestException(
        'Old password and new password are required',
      );
    }

    const user = await this.userModel.findById(userId).select('+password');
    if (!user) {
      throw new NotFoundException(`User not found with ID: ${userId}`);
    }

    if (!user.password) {
      throw new InternalServerErrorException(
        'Password field is missing for the user',
      );
    }

    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password);
    if (!isOldPasswordValid) {
      throw new UnauthorizedException('Invalid old password');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    await this.userModel.findByIdAndUpdate(
      userId,
      { password: hashedNewPassword },
      { new: true },
    );

    return { message: 'Password changed successfully' };
  }

  /**
   * Send email verification to user
   */
  async sendVerificationEmail(email: string): Promise<{ message: string; status: number }> {
    try {
      // Check rate limiting
      const rateLimitResult = emailVerificationRateLimiter.isAllowed(email.toLowerCase());
      if (!rateLimitResult.isAllowed) {
        const remainingTime = formatRemainingTime(rateLimitResult.remainingTime!);
        throw new HttpException(
          {
            status: HttpStatus.TOO_MANY_REQUESTS,
            message: `Too many verification email requests. Please try again in ${remainingTime}.`,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      const user = await this.userModel.findOne({ email: email.toLowerCase() });

      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.NOT_FOUND,
            message: 'User not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      if (user.isEmailVerified) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Email is already verified',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Generate new verification token
      const { token, expiry } = generateTokenWithExpiry();

      // Update user with new token
      await this.userModel.findByIdAndUpdate(user._id, {
        verificationToken: token,
        tokenExpiry: expiry,
      });

      // Get base URL from config or use default
      const baseUrl = this.configService.get<string>('BASE_URL', 'http://localhost:3000');
      const verificationUrl = `${baseUrl}/api/auth/verify-email/${token}`;

      // Generate email content
      const htmlContent = generateVerificationEmailHtml({
        email: user.email,
        verificationUrl,
        appName: 'VitaeChek',
      });

      const textContent = generateVerificationEmailText({
        email: user.email,
        verificationUrl,
        appName: 'VitaeChek',
      });

      const subject = generateVerificationEmailSubject('VitaeChek');

      // Send email
      await this.mailerService.sendMail({
        to: user.email,
        subject,
        html: htmlContent,
        text: textContent,
      });

      return {
        message: `Verification email sent to ${user.email}`,
        status: 200,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('Error sending verification email:', error);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to send verification email. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Verify email using token
   */
  async verifyEmail(token: string): Promise<{ message: string; status: number }> {
    try {
      const user = await this.userModel.findOne({ verificationToken: token });

      if (!user) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Invalid verification token',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (user.isEmailVerified) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Email is already verified',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!user.tokenExpiry || isTokenExpired(user.tokenExpiry)) {
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST,
            message: 'Verification token has expired. Please request a new verification email.',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      // Update user as verified and clear token
      await this.userModel.findByIdAndUpdate(user._id, {
        isEmailVerified: true,
        verificationToken: null,
        tokenExpiry: null,
      });

      return {
        message: 'Email verified successfully',
        status: 200,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('Error verifying email:', error);
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to verify email. Please try again later.',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Resend verification email
   */
  async resendVerificationEmail(resendVerificationDto: ResendVerificationDto): Promise<{ message: string; status: number }> {
    return this.sendVerificationEmail(resendVerificationDto.email);
  }

}
