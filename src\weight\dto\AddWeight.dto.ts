import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";
import { IWeightGoal } from "../interfaces/weight.interface";

export class AddWeightDto {
    @IsString()
    @IsNotEmpty()
    @ApiProperty({
        description: 'User ID associated with this weight record',
        example: '507f1f77bcf86cd799439012',
    })
    userId: string;

    @IsNotEmpty()
    @ApiProperty({
        description: 'Weight measured',
        example: 70,
    })
    weight: number;


    @IsNotEmpty()
    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
    measuredAt: Date;


    @IsString()
    @ApiProperty({
        description: 'Method used for input (manual, device)',
        example: 'manual',
        enum: ['manual', 'device'],
        required: false,
    })
    inputMethod?: 'manual' | 'device';


    @IsString()
    @IsOptional()
    @ApiProperty({
        description: 'Device ID used for measurement',
        example: '12345',
        required: false,
    })
    deviceId?: string;


    @IsString()
    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Feeling dizzy',
        required: false,
    })
    comment?: string;

    @IsOptional()
    @ApiProperty({
        description: 'Goal for weight management',
        example: {
            goalType: 'lose',
            targetWeight: 65,
            targetBodyFatPercentage: 10,
            startDate: '2024-01-15T10:30:00.000Z',
            targetDate: '2025-01-15T10:30:00.000Z',
            reminderEnabled: true,
            reminderTime: '09:00',
        },
    })
    goal?: IWeightGoal;

}

