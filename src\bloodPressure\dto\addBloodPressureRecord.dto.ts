import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>rray, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";

export class AddBloodPressureRecordDto {
    @ApiProperty({
        description: 'User ID associated with this blood pressure measurement',
        example: '507f1f77bcf86cd799439012',
    })
    @IsString()
    @IsNotEmpty()
    userId: string;


    @ApiProperty({
        description: 'Type of measurement (pre-meal, post-meal, fasting, bedTime)',
        example: 'pre-meal',
        enum: ['pre-meal','post-meal','fasting','bedTime'],
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    measurementType: string;

    @ApiProperty({
        description: 'Systolic blood pressure',
        example: 120,
        required: false,
    })

    @IsNumber()
    systolic?: number;


    @ApiProperty({
        description: 'Diastolic blood pressure',
        example: 80,
        required: false,
    })
    @IsNumber()
    diastolic?: number;


    @ApiProperty({
        description: 'Glucose level',
        example: 100,
        required: false,
    })
    @IsNumber()
    glucose?: number;


    @ApiProperty({
        description: 'Pulse rate',
        example: 70,
        required: false,
    })
    @IsNumber()
    pulse?: number;


    @ApiProperty({
        description: 'Body mass index',
        example: 25,
        required: false,
    })
    @IsNumber()
    bmi?: number;

    @IsNotEmpty()
    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
    measuredAt: Date;


    @ApiProperty({
        description: 'Method used for input (manual, device, camera)',
        example: 'manual',
        enum: ['manual', 'device', 'camera'],
        required: false,
    })
    @IsString()
    inputMethod?: 'manual' | 'device' | 'camera';


    @ApiProperty({
        description: 'Device ID used for measurement',
        example: '12345',
        required: false,
    })
    @IsString()
    @IsOptional()
    deviceId?: string;


    @ApiProperty({
        description: 'Tags associated with the measurement',
        example: ['morning', 'before exercise'],
        required: false,
    })
    @IsArray()
    tags?: string[];

    @IsString()
    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Feeling dizzy',
        required: false,
    })
    comment?: string;
}
