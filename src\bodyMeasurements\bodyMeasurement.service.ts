import { HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BodyMeasurement } from './schema/bodyMeasure.schema';
import { Model } from 'mongoose';
import { IBodyMeasurement } from './interfaces/bodyMeasure.interfaces';
import { CreateBodyMeasureDto } from './dto/createBodyMeasure.dto';
import { updateBodyMeasurementDto } from './dto/updateBodyMeasure.dto';

@Injectable()
export class BodyMeasurementService {
  constructor(
    @InjectModel(BodyMeasurement.name)
    private bodyMeasureModel: Model<IBodyMeasurement>,
  ) {}

  async create(bodyMeasureDto: CreateBodyMeasureDto): Promise<IBodyMeasurement> {
    try {
      const record= (await this.bodyMeasureModel.create(bodyMeasureDto)).populate({path:'userId',select:'-password'});
      return record;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to create body measurement: ${error.message}`);
    }
  }

  async findAll(): Promise<IBodyMeasurement[]> {
    try {
      return await this.bodyMeasureModel.find().populate({path:'userId',select:'-password'}).exec();
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch body measurements: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<IBodyMeasurement> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findById(id).populate({path:'userId',select:'-password'}).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return bodyMeasure;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch body measurement: ${error.message}`);
    }
  }

  async update(id: string, bodyMeasureDto: updateBodyMeasurementDto): Promise<IBodyMeasurement> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findByIdAndUpdate(id, bodyMeasureDto, { new: true }).populate({path:'userId',select:'-password'}).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return bodyMeasure;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update body measurement: ${error.message}`);
    }
  }

  async delete(id: string): Promise<string> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findByIdAndDelete(id).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return 'Body measurement deleted';
    } catch (error) {
      throw new InternalServerErrorException(`Failed to delete body measurement: ${error.message}`);
    }
  }
}
