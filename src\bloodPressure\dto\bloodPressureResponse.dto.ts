import { ApiProperty } from "@nestjs/swagger";

export class BloodPressureResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the blood pressure measurement record',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;
    @ApiProperty({
        description: 'User ID associated with this blood pressure measurement',
        example: '507f1f77bcf86cd799439012',
    })
    userId: string;
    @ApiProperty({
        description: 'Type of measurement (pre-meal, post-meal, fasting, bedTime)',
        example: 'pre-meal',
        enum: ['pre-meal','post-meal','fasting','bedTime'],
        required: true,
    })
    measurementType: string;
    @ApiProperty({
        description: 'Systolic blood pressure',
        example: 120,
        required: false,
    })
    systolic?: number;
    @ApiProperty({
        description: 'Diastolic blood pressure',
        example: 80,
        required: false,
    })
    diastolic?: number;
    @ApiProperty({
        description: 'Glucose level',
        example: 100,
        required: false,
    })
    glucose?: number;
    @ApiProperty({
        description: 'Pulse rate',
        example: 70,
        required: false,
    })
    pulse?: number;
    @ApiProperty({
        description: 'Body mass index',
        example: 25,
        required: false,
    })
    bmi?: number;
    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
    measuredAt: Date;
    @ApiProperty({
        description: 'Method used for input (manual, device, camera)',
        example: 'manual',
        enum: ['manual', 'device', 'camera'],
        required: false,
    })
    
    inputMethod?: 'manual' | 'device' | 'camera';
    @ApiProperty({
        description: 'Device ID used for measurement',
        example: '12345',
        required: false,
    })
    deviceId?: string;
    @ApiProperty({
        description: 'Tags associated with the measurement',
        example: ['morning', 'before exercise'],
        required: false,
    })
    tags?: string[];
    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Feeling dizzy',
        required: false,
    })
    comment?: string;
    @ApiProperty({
        description: 'Timestamp when the blood pressure measurement was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Timestamp when the blood pressure measurement was last updated',
        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}

