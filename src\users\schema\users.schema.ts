import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

@Schema({_id:false})
export class GlycemicMonitoringData {
  @Prop({ type: String })
  hba1c?: string;

  @Prop({ type: String })
  fastingGlucose?: string;

  @Prop({ type: String })
  lastTestInterval?: string;

  @Prop({ type: Boolean, default: false })
  hba1cNotRemembered?: boolean;

  @Prop({ type: Boolean, default: false })
  lastTestMoreThanYear?: boolean;
}

@Schema({_id:false})
export class BloodPressureData {
  @Prop({ type: String })
  systolic?: string;

  @Prop({ type: String })
  diastolic?: string;

  @Prop({ type: String })
  pulse?: string;

  @Prop({ type: Boolean, default: false })
  bpNotRemembered?: boolean;
}

@Schema({_id:false})
export class LifeCraftData {
  @Prop({ type: Boolean, default: false })
  analyzing?: boolean;

  @Prop({ type: <PERSON>olean, default: false })
  plan?: boolean;
}

@Schema({_id:false})
export class RemindersData {
  @Prop({ type: Boolean, default: false })
  bloodPressure?: boolean;
}

@Schema({_id:false})
export class HealthAssessmentData {
  @Prop({ type: String })
  gender?: string;

  @Prop({ type: String })
  ageRange?: string;

  @Prop({ type: String })
  weight?: string;

  @Prop({ type: String })
  height?: string;

  @Prop({ type: String })
  bmi?: string;

  @Prop({ type: [String] })
  conditionsToManage?: string[];

  @Prop({ type: String })
  diabetesType?: string;

  @Prop({ type: String })
  diabetesDiagnosedSince?: string;

  @Prop({ type: String })
  medicationStatus?: string;

  @Prop({ type: [String] })
  medicationType?: string[];

  @Prop({ type: GlycemicMonitoringData })
  glycemicMonitoring?: GlycemicMonitoringData;

  @Prop({ type: String })
  hypertensionStatus?: string;

  @Prop({ type: String })
  hypertensionSince?: string;

  @Prop({ type: BloodPressureData })
  bloodPressure?: BloodPressureData;

  @Prop({ type: [String] })
  comorbidities?: string[];

  @Prop({ type: String })
  stressLevel?: string;

  @Prop({ type: String })
  emotion?: string;

  @Prop({ type: String })
  sleepQuality?: string;

  @Prop({ type: String })
  fallAsleep?: string;

  @Prop({ type: String })
  fallAsToNightleep?: string;

  @Prop({ type: String })
  diabetesLevel?: string;

  @Prop({ type: String })
  fitnessLevel?: string;

  @Prop({ type: LifeCraftData })
  lifeCraft?: LifeCraftData;

  @Prop({ type: RemindersData })
  reminders?: RemindersData;
}

@Schema({ timestamps: true })
export class Users {
  @Prop({ required: false })
  firstName?: string;

  @Prop({ required: false })
  lastName?: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ required: false })
  mobileNumber?: string;

  @Prop({ required: false })
  gender?: string;

  @Prop({ required: false })
  dob?: string;

  @Prop({ required: false, default: false })
  isVerified: boolean;

  @Prop({ type: HealthAssessmentData })
  healthAssessmentData?: HealthAssessmentData;

  @Prop({required:false})
  fcmToken?: string;
}

export const UsersSchema = SchemaFactory.createForClass(Users);
