import { MongooseModule } from "@nestjs/mongoose";
import { BloodPressure,BloodPressureSchema } from "./schema/bloodPressure.schema";
import { AuthModule } from "src/auth/auth.module";
import { BloodPressureService } from "./bloodPressure.service";
import { BloodPressureController } from "./bloodPressure.controller";
import { Module } from "@nestjs/common";

@Module({
    imports:[
        MongooseModule.forFeature([
            {name:BloodPressure.name,schema:BloodPressureSchema}
        ]),
        AuthModule,
    ],
    providers:[BloodPressureService],
    controllers:[BloodPressureController],
    exports:[BloodPressureService]
})
export class BloodPressureModule{}