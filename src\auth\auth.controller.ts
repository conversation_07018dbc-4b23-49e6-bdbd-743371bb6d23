import { Body, Controller, Post, UseGuards, Req } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { IAuth, ISignUp } from "./interfaces/auth.interfaces";
import { CreateUserDto } from "src/users/dto/createUser.dto";
import { signInDto } from "./dto/signIn.dto";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBadRequestResponse,
    ApiConflictResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth
} from '@nestjs/swagger';
import { AuthResponseDto } from './dto/auth-response.dto';
import { ErrorResponseDto, ValidationErrorResponseDto, UnauthorizedErrorResponseDto } from '../common/dto/error-response.dto';
import { ForgotPasswordDto } from "./dto/forget-password.dto";
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ChangePasswordDto } from "./dto/change-password.dto";
import { VerifyOtpDTO } from "./dto/verify-otp.dto";
import { AuthGuard } from "./auth.guard";
import { signUpDto } from "./dto/signUp.dto";

@ApiTags('Auth')
@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post("signup")
    @ApiOperation({
        summary: 'User registration',
        description: 'Register a new user account and receive an authentication token',
    })
    @ApiBody({
        type: signUpDto,
        description: 'User registration data',
    })
    @ApiResponse({
        status: 201,
        description: 'User registered successfully',
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Validation error or invalid input data',
        type: ValidationErrorResponseDto,
    })
    @ApiConflictResponse({
        description: 'User already exists with this email',
        type: ErrorResponseDto,
        schema: {
            example: {
                statusCode: 409,
                message: 'User already exists',
                error: 'Conflict',
                timestamp: '2024-01-15T10:30:00.000Z',
                path: '/api/auth/signup'
            }
        }
    })
    async signUp(@Body() signUpDto:signUpDto):Promise<ISignUp>{
        return this.authService.signUp(signUpDto);
    }

    @Post("signin")
    @ApiOperation({
        summary: 'User login',
        description: 'Authenticate user credentials and receive an authentication token',
    })
    @ApiBody({
        type: signInDto,
        description: 'User login credentials',
    })
    @ApiResponse({
        status: 200,
        description: 'User authenticated successfully',
        type: AuthResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Validation error or invalid input data',
        type: ValidationErrorResponseDto,
    })
    @ApiUnauthorizedResponse({
        description: 'Invalid credentials',
        type: UnauthorizedErrorResponseDto,
    })
    async signIn(@Body() signInDto:signInDto):Promise<IAuth>{
        return this.authService.signIn(signInDto);
    }

    @Post('forgot-password')
    @ApiOperation({
        summary: 'Forgot password',
        description: 'Send OTP to reset password',
    })
    @ApiBody({
        type: ForgotPasswordDto,
        description: 'User email for OTP',
    })
    @ApiResponse({
        status: 200,
        description: 'OTP sent successfully',
        schema: {
            example: {
                message: 'OTP <NAME_EMAIL>',
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Invalid email',
        type: ErrorResponseDto,
    })
    async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
        return this.authService.forgotPassword(forgotPasswordDto);
    }

    @Post('verify-otp')
    @ApiOperation({
        summary: 'Verify OTP',
        description: 'Verify OTP sent for password reset',
    })
    @ApiBody({
        type: VerifyOtpDTO,
        description: 'OTP and email',
    })
    @ApiResponse({
        status: 200,
        description: 'OTP verified successfully',
        schema: {
            example: {
                message: 'OTP verified',
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Invalid OTP',
        type: ErrorResponseDto,
    })
    async verifyOtp(@Body() verifyOtpDto: VerifyOtpDTO) {
        return this.authService.verifyOtp(verifyOtpDto);
    }

@Post('reset-password')
    @ApiOperation({
        summary: 'Reset password',
        description: 'Reset password using OTP',
    })
    @ApiBody({
        type: ResetPasswordDto,
        description: 'New password and email',
    })
    @ApiResponse({
        status: 200,
        description: 'Password reset successfully',
        schema: {
            example: {
                message: 'PASSWORD RESTORED - 200',
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Invalid email',
        type: ErrorResponseDto,
    })
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
        return this.authService.resetPassword(resetPasswordDto);
    }

    @UseGuards(AuthGuard)
    @Post('change-password')

    @ApiBearerAuth('Bearer-auth')
    @ApiOperation({
        summary: 'Change password',
        description: 'Change password using old password',
    })
    @ApiBody({
        type: ChangePasswordDto,
        description: 'Old password and new password',
    })
    @ApiResponse({
        status: 200,
        description: 'Password changed successfully',
        schema: {
            example: {
                message: 'Password changed successfully',
            },
        },
    })
    @ApiBadRequestResponse({
        description: 'Old password and new password are required',
        type: ErrorResponseDto,
    })
    @ApiUnauthorizedResponse({
        description: 'Invalid old password',
        type: UnauthorizedErrorResponseDto,
    })
    async changePassword(
        @Req() req,
        @Body() changePasswordDto: ChangePasswordDto,
        
    ) {
        const userId = req.user._id;
        return this.authService.changePassword(
            userId,
            changePasswordDto,
        );
    }
}