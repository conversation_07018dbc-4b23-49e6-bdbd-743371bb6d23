import { ApiProperty } from '@nestjs/swagger';

export class EmailVerificationResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Email verified successfully',
  })
  message: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 200,
  })
  status: number;
}

export class ResendVerificationResponseDto {
  @ApiProperty({
    description: 'Success message indicating verification email was sent',
    example: 'Verification email <NAME_EMAIL>',
  })
  message: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 200,
  })
  status: number;
}
