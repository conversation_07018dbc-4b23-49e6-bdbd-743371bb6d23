import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { AllExceptionsFilter } from './ultils/allExceptionsFilter';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule,{bodyParser:true});

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global filters
  app.useGlobalFilters(new AllExceptionsFilter());

  // Global prefix
  app.setGlobalPrefix('api');

  // Swagger/OpenAPI configuration
  const config = new DocumentBuilder()
    .setTitle('VitaeChek Backend API')
    .setDescription('A comprehensive health management backend API that provides user authentication, profile management, and health tracking capabilities.')
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'Token',
        name: 'Authorization',
        description: 'Enter your Bearer token',
        in: 'header',
      },
      'Bearer-auth', // This name here is important for matching up with @ApiBearerAuth() in your controller!
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
  swaggerOptions: {
    persistAuthorization: true,
    tagsSorter: 'alpha',
    operationsSorter: 'alpha',
  },
  customSiteTitle: 'VitaeChek API Documentation',
  customfavIcon: '/favicon.ico',
  customCss: `
    .swagger-ui .topbar { display: none; }

    /* Centered and styled title */
    .swagger-ui .info {
      text-align: center;
    }

    .swagger-ui .info .title {
      color: #3b82f6;
      font-size: 3rem;
      font-weight: 800;
      font-family: 'Segoe UI', sans-serif;
      margin-bottom: 0.5rem;
    }

    .swagger-ui .info .description {
      font-size: 1rem;
      color: #374151;
      font-family: 'Segoe UI', sans-serif;
      margin-bottom: 1.5rem;
    }

    .swagger-ui .opblock-tag.no-desc span {
      font-size: 1.25rem;
      background-color: #3b82f6 !important;
      color: white !important;
      padding: 6px 12px;
      border-radius: 8px;
    }

    .swagger-ui .opblock {
      border-radius: 10px;
      margin-bottom: 1.5rem;
      border: 1px solid #d1d5db;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .swagger-ui .opblock-summary {
      background-color: #f9fafb;
      border-radius: 10px 10px 0 0;
      padding: 10px 16px;
    }

    .swagger-ui .btn {
      background-color: #3b82f6 !important;
      border-color: #3b82f6 !important;
      color: #fff !important;
      border-radius: 6px !important;
      padding: 8px 12px !important;
      font-weight: 500;
      gap: 2px;
    }

    .swagger-ui .btn.cancel {
      background-color: #ef4444 !important;
      border-color: #ef4444 !important;
    }

    .swagger-ui .responses-inner {
      background-color: #f9fafb;
      padding: 12px;
      border-radius: 8px;
    }

    .swagger-ui input, .swagger-ui textarea {
      border-radius: 6px;
      border: 1px solid #d1d5db;
      padding: 6px 12px;
    }
  `,
});


  await app.listen(3000);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(`Swagger documentation available at: ${await app.getUrl()}/api/docs`);
}
bootstrap();
