import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { BodyMeasurement, BodyMeasurementSchema } from "./schema/bodyMeasure.schema";
import { BodyMeasurementService } from "./bodyMeasurement.service";
import { BodyMeasurementController } from "./bodyMeasurement.controller";
import { AuthModule } from "src/auth/auth.module";

@Module({
    imports:[
        MongooseModule.forFeature([
            {name:BodyMeasurement.name,schema:BodyMeasurementSchema}
        ]),
        AuthModule,
    ],
    providers:[BodyMeasurementService],
    controllers:[BodyMeasurementController],
    exports:[BodyMeasurementService]
})
export class BodyMeasurementModule{}