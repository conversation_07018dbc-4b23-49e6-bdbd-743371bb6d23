import { ApiProperty } from "@nestjs/swagger";

export class BloodSugarResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the blood sugar measurement record',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;

    @ApiProperty({
        description: 'User ID associated with this blood sugar measurement',
        example: '507f1f77bcf86cd799439012',
    })
    userId: string;

    @ApiProperty({
        description: 'Glycemic level measured',
        example: 100,
    })
    glycemic?: number;

    @ApiProperty({
        description: 'Ketone level measured',
        example: 0.5,
    })
    ketone?: number;

    @ApiProperty({
        description: 'Type of measurement (pre-meal, post-meal, fasting, bedTime)',
        example: 'pre-meal',
        enum: ['pre-meal','post-meal','fasting','bedTime'],
        required: false,
    })
    measurementType?: string;

    @ApiProperty({
        description: 'Source of the measurement (Manual, Device, Camera)',
        example: 'Manual',
        enum: ['Manual', 'Device', 'Camera'],
        required: false,
    })
    source?: string;

    @ApiProperty({
        description: 'Name of the device used for measurement',
        example: 'OneTouch Ultra 2',
        required: false,
    })
    deviceName?: string;

    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Meal was high in carbs',
        required: false,
    })
    comment?: string;

    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
    measurementDate: string;

    @ApiProperty({
        description: 'Activities performed before the measurement',
        example: ['Exercise', 'Meal'],
        required: false,
    })
    preMeasurementActivities?: string[];

    @ApiProperty({
        description: 'Custom note for pre-measurement activities',
        example: 'Had a high-carb meal',
        required: false,
    })
    customActivityNote?: string;

    @ApiProperty({
        description: 'Timestamp when the blood sugar measurement was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Timestamp when the blood sugar measurement was last updated',
        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}
