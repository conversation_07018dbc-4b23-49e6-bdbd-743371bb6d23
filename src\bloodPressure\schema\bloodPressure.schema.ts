import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
@Schema({ timestamps: true })
export class BloodPressure {
  @Prop({ required: true })
  userId: string;

  @Prop({ enum: ['pre-meal','post-meal','fasting','bedTime'], required: true })
  measurementType: string;

  @Prop()
  systolic?: number;

  @Prop()
  diastolic?: number;

  @Prop()
  glucose?: number;

  @Prop()
  pulse?: number;

  @Prop()
  bmi?: number;

  @Prop({ required:true })
  measuredAt: Date;

  @Prop({ enum: ['manual', 'device', 'camera'], default: 'manual' })
  inputMethod: 'manual' | 'device' | 'camera';

  @Prop()
  deviceId?: string;

  @Prop({ type: [String], default: [] })
  tags?: string[];

  @Prop()
  comment?: string;
}

export const BloodPressureSchema = SchemaFactory.createForClass(BloodPressure);
