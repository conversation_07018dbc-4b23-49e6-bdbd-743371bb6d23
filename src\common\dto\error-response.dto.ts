import { ApiProperty } from '@nestjs/swagger';

export class ErrorResponseDto {
    @ApiProperty({
        description: 'HTTP status code',
        example: 400,
    })
    statusCode: number;

    @ApiProperty({
        description: 'Error message or array of error messages',
        example: 'Validation failed',
        oneOf: [
            { type: 'string' },
            { type: 'array', items: { type: 'string' } }
        ],
    })
    message: string | string[];

    @ApiProperty({
        description: 'Error type or path',
        example: 'Bad Request',
    })
    error: string;

    @ApiProperty({
        description: 'Timestamp when the error occurred',
        example: '2024-01-15T10:30:00.000Z',
    })
    timestamp: string;

    @ApiProperty({
        description: 'API path where the error occurred',
        example: '/api/auth/signup',
    })
    path: string;
}

export class ValidationErrorResponseDto extends ErrorResponseDto {
    @ApiProperty({
        description: 'Array of validation error messages',
        example: ['email must be a valid email', 'password must be longer than or equal to 6 characters'],
        type: [String],
    })
    message: string[];
}

export class UnauthorizedErrorResponseDto extends ErrorResponseDto {
    @ApiProperty({
        description: 'Unauthorized error message',
        example: 'Invalid or expired accessToken',
    })
    message: string;

    @ApiProperty({
        description: 'Error type',
        example: 'Unauthorized',
    })
    error: string;
}
