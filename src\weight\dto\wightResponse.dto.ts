import { ApiProperty } from "@nestjs/swagger";
import { IWeightGoal } from "../interfaces/weight.interface";

export class WeightResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the weight record',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;
    @ApiProperty({
        description: 'User ID associated with this weight record',
        example: '507f1f77bcf86cd799439012',
    })
    userId: string;
    @ApiProperty({
        description: 'Weight measured',
        example: 70,
    })
    weight: number;
    @ApiProperty({
        description: 'Body mass index',
        example: 25,
        required: false,
    })
    bmi?: number;
    @ApiProperty({
        description: 'Body fat percentage',
        example: 15,
        required: false,
    })
    bodyFatPercentage?: number;
    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
    measuredAt: Date;
    @ApiProperty({
        description: 'Method used for input (manual, device)',
        example: 'manual',
        enum: ['manual', 'device'],
        required: false,
    })
    inputMethod?: 'manual' | 'device';
    @ApiProperty({
        description: 'Device ID used for measurement',
        example: '12345',
        required: false,
    })
    deviceId?: string;
    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Feeling dizzy',
        required: false,
    })
    comment?: string;
    @ApiProperty({
        description: 'Goal for weight management',
        example: {
            goalType: 'lose',
            targetWeight: 65,
            targetBodyFatPercentage: 10,
            startDate: '2024-01-15T10:30:00.000Z',

            targetDate: '2025-01-15T10:30:00.000Z',
            reminderEnabled: true,
            reminderTime: '09:00',
        },
    })
    goal?: IWeightGoal;
    @ApiProperty({
        description: 'Timestamp when the weight record was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;
    @ApiProperty({
        description: 'Timestamp when the weight record was last updated',

        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}

