import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { BloodPressureService } from "./bloodPressure.service";
import { IMeta, IQueryResponse } from "src/interfaces/query-response";
import { IBloodPressure } from "./interfaces/bloodPressure.interface";
import { AddBloodPressureRecordDto } from "./dto/addBloodPressureRecord.dto";
import { UpdateBloodPressureRecordDto } from "./dto/updateBloodPressureRecord.dto";
import { BloodPressureResponseDto } from "./dto/bloodPressureResponse.dto";
import { AuthGuard } from "src/auth/auth.guard";

@ApiTags('BloodPressure')
@UseGuards(AuthGuard)
@ApiBearerAuth('Bearer-auth')
@Controller('blood-pressure')
export class BloodPressureController {
    constructor(private readonly bloodPressureService: BloodPressureService) {}

    @Post()
    @ApiOperation({
        summary: 'Create blood pressure measurement',
        description: 'Create a new blood pressure measurement record',
    })
    @ApiBody({
        type: AddBloodPressureRecordDto,
        description: 'Blood pressure measurement data',
    })
    @ApiResponse({
        status: 201,
        description: 'Blood pressure measurement created',
        type: BloodPressureResponseDto,
    })
    async createBloodPressure(@Body() bloodPressureDto: AddBloodPressureRecordDto) {
        return this.bloodPressureService.create(bloodPressureDto);
    }

    @Get()
    @ApiOperation({
        summary: 'Get all blood pressure measurements',
        description: 'Retrieve all blood pressure measurement records',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood pressure measurements found',
        type: [BloodPressureResponseDto],
    })
    async getAllBloodPressureMeasurements():Promise<IQueryResponse<IBloodPressure>> {
        const bloodPressureMeasurements = await this.bloodPressureService.findAll();
        const meta: IMeta = {
            totalSize: bloodPressureMeasurements.length,
            // chunk: 1,
            // page: 1,
            // limit: bodyMeasurements.length,
        };
        return { meta, results: bloodPressureMeasurements };
    }
    @Get(':id')
    @ApiOperation({
        summary: 'Get blood pressure measurement by ID',
        description: 'Retrieve blood pressure measurement by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood pressure measurement found',
        type: BloodPressureResponseDto,
    })
    async getBloodPressureById(@Param('id') id: string) {
        return this.bloodPressureService.findOne(id);
    }
    @Patch(':id')
    @ApiOperation({
        summary: 'Update blood pressure measurement by ID',
        description: 'Update blood pressure measurement by its unique identifier',
    })
    @ApiBody({
        type: UpdateBloodPressureRecordDto,
        description: 'Blood pressure measurement data',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood pressure measurement updated',
        type: BloodPressureResponseDto,
    })
    async updateBloodPressure(
        @Param('id') id: string,
        @Body() bloodPressureDto: UpdateBloodPressureRecordDto,
    ) {
        return this.bloodPressureService.update(id, bloodPressureDto);
    }
    @Delete(':id')
    @ApiOperation({
        summary: 'Delete blood pressure measurement by ID',
        description: 'Delete blood pressure measurement by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood pressure measurement deleted',
        type: BloodPressureResponseDto,
    })
    async deleteBloodPressure(@Param('id') id: string) {
        return this.bloodPressureService.delete(id);
    }
}