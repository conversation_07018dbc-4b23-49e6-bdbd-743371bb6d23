import { ApiProperty } from '@nestjs/swagger';
import { IMeasurementGoal, IMeasurementRecord } from '../interfaces/bodyMeasure.interfaces';
import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateBodyMeasureDto {

    @ApiProperty({
        description: 'User ID associated with this body measurement',
        example: '507f1f77bcf86cd799439012',
    })
   @IsNotEmpty()
    userId: string;

    @ApiProperty({
        description: 'Measurement record for the neck',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    neck: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the chest',
        example: {
            value: 40,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    chest: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the shoulder',
        example: {
            value: 42,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    shoulder: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    leftArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    rightArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the waist',
        example: {
            value: 30,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    waist: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the abdomen',
        example: {
            value: 34,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    abdomen: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the hip',
        example: {
            value: 38,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    hip: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    leftThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    rightThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    leftCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
   @IsOptional()
    rightCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement goal for a specific body part',
        example: {
            target: 32,
            bodyPart: 'waist',
        },
    })
   @IsOptional()
    goals: IMeasurementGoal;
    @ApiProperty({
        description: 'Custom measurements for additional body parts',
        example: new Map([['bicep', { value: 30, recordedAt: '2024-01-15T10:30:00.000Z' }]]),
    })
    @IsOptional()
    customMeasurements: Map<string, IMeasurementRecord>;

}
