import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { BloodSugarService } from "./bloodSugar.service";
import { CreateBloodSugarDto } from "./dto/createBloodSugar.dto";
import { BloodSugarResponseDto } from "./dto/bloodSugarResponse.dto";
import { AuthGuard } from "src/auth/auth.guard";
import { UpdateBloodSugarDto } from "./dto/updateBloodSugar.dto";
import { IQueryResponse, IMeta } from "src/interfaces/query-response";

@ApiTags('BloodSugar')
@UseGuards(AuthGuard)
@Controller('blood-sugar')
export class BloodSugarController {
    constructor(private readonly bloodSugarService: BloodSugarService) {}

    @ApiOperation({
        summary: 'Create blood sugar measurement',
        description: 'Create a new blood sugar measurement record',
    })
    @ApiBody({
        type: CreateBloodSugarDto,
        description: 'Blood sugar measurement data',
    })
    @ApiResponse({
        status: 201,
        description: 'Blood sugar measurement created',
        type: BloodSugarResponseDto,
    })

    @Post()
    @ApiBearerAuth('Bearer-auth')
    async createBloodSugar(@Body() bloodSugarDto: CreateBloodSugarDto) {
        return this.bloodSugarService.create(bloodSugarDto);
    }

    @Get()
    @ApiBearerAuth('Bearer-auth')
    @ApiOperation({
        summary: 'Get all blood sugar measurements',
        description: 'Retrieve all blood sugar measurement records',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood sugar measurements found',
        type: [BloodSugarResponseDto],
    })
    async getAllBloodSugarMeasurements():Promise<IQueryResponse<BloodSugarResponseDto>> {
        const bloodSugarMeasurements = await this.bloodSugarService.findAll();
        const meta: IMeta = {
            totalSize: bloodSugarMeasurements.length,
            // chunk: 1,
            // page: 1,
            // limit: bodyMeasurements.length,
        };
        return { meta, results: bloodSugarMeasurements };
    }
    @Get(':id')
    @ApiBearerAuth('Bearer-auth')
    @ApiOperation({
        summary: 'Get blood sugar measurement by ID',
        description: 'Retrieve blood sugar measurement by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood sugar measurement found',
        type: BloodSugarResponseDto,
    })
    async getBloodSugarById(@Param('id') id: string) {
        return this.bloodSugarService.findOne(id);
    }

    @Patch(':id')
    @ApiBearerAuth('Bearer-auth')
    @ApiOperation({
        summary: 'Update blood sugar measurement by ID',
        description: 'Update blood sugar measurement by its unique identifier',
    })
    @ApiBody({
        type: CreateBloodSugarDto,
        description: 'Blood sugar measurement data',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood sugar measurement updated',
        type: BloodSugarResponseDto,
    })
    async updateBloodSugar(
        @Param('id') id: string,
        @Body() bloodSugarDto: UpdateBloodSugarDto,
    ) {
        return this.bloodSugarService.update(id, bloodSugarDto);
    }

    @Delete(':id')
    @ApiBearerAuth('Bearer-auth')
    @ApiOperation({
        summary: 'Delete blood sugar measurement by ID',
        description: 'Delete blood sugar measurement by its unique identifier',
    })
    @ApiResponse({
        status: 200,
        description: 'Blood sugar measurement deleted',
        type: BloodSugarResponseDto,
    })
    async deleteBloodSugar(@Param('id') id: string) {
        return this.bloodSugarService.delete(id);
    }   
}